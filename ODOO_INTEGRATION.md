# Odoo ERP Integration for Storyblok Field Plugin

This document describes the Odoo ERP integration implementation for the Storyblok field plugin that allows users to pick products from an Odoo instance.

## Overview

The plugin has been transformed from using mock data to connecting with a real Odoo ERP system via JSON-RPC API calls. This enables users to:

- Browse and search products from their Odoo product catalog
- Filter products by categories
- Support pagination for large product catalogs
- Select products to use in their Storyblok content

## Configuration

### Field Plugin Options

When adding this field plugin to your Storyblok space, you need to configure the following options:

- **Odoo URL**: The base URL of your Odoo instance (e.g., `https://your-odoo.com`)
- **Database Name**: The name of your Odoo database
- **Username**: Your Odoo username
- **Password**: Your Odoo password
- **Items Limit** (optional): Maximum number of items that can be selected

### Odoo Requirements

Your Odoo instance must:
- Be accessible via HTTPS (recommended for security)
- Have the `product.template` and `product.category` models available
- Allow JSON-RPC API access for the configured user
- Have products with the following fields:
  - `name` (required)
  - `default_code` (SKU, optional)
  - `list_price` (optional)
  - `image_1920` (product image, optional)
  - `description` (optional)
  - `categ_id` (category, optional)
  - `active` (must be True for products to appear)

## Architecture

### Key Components

1. **OdooApiService** (`src/services/odoo-api.ts`)
   - Handles JSON-RPC communication with Odoo
   - Manages authentication and session handling
   - Provides methods for product and category queries

2. **OdooDataService** (`src/data/odoo-data.ts`)
   - High-level data service that uses OdooApiService
   - Implements caching for categories
   - Transforms Odoo data to plugin format

3. **Data Transformers** (`src/utils/odoo-transformer.ts`)
   - Converts Odoo product data to BasketItem format
   - Handles category transformations
   - Provides validation utilities

4. **Type Definitions** (`src/types/odoo.ts`)
   - TypeScript interfaces for Odoo API communication
   - Configuration types
   - Data structure definitions

### API Integration

The plugin uses Odoo's JSON-RPC API with the following endpoints:

- **Authentication**: `/jsonrpc` with `common.authenticate`
- **Product Search**: `/jsonrpc` with `object.execute_kw` on `product.template`
- **Category Search**: `/jsonrpc` with `object.execute_kw` on `product.category`

### Data Flow

1. User configures Odoo connection details in Storyblok
2. Plugin validates configuration and authenticates with Odoo
3. Categories are loaded once and cached for filtering
4. Products are loaded dynamically based on:
   - Search terms (matches product name and SKU)
   - Category filters
   - Pagination parameters

### Caching Strategy

- **Categories**: Cached for 5 minutes to reduce API calls
- **Products**: Not cached, always fetched fresh to ensure up-to-date data
- **Authentication**: Session maintained per OdooApiService instance

## Error Handling

The plugin provides user-friendly error messages for common issues:

- **Authentication failures**: "Please check your Odoo credentials"
- **Network errors**: "Please check your Odoo URL and network connection"
- **Database errors**: "Please check your database name"
- **API errors**: Detailed error messages from Odoo

## Security Considerations

- Credentials are passed through Storyblok's secure options system
- HTTPS is recommended for all Odoo connections
- No credentials are stored in the plugin code
- API calls are made directly from the browser to Odoo (CORS must be configured)

## Performance Optimizations

- Server-side filtering reduces data transfer
- Pagination limits the number of products loaded at once
- Category caching reduces redundant API calls
- Debounced search prevents excessive API requests

## Testing

To test the integration:

1. Configure a test Odoo instance with sample products
2. Set up the field plugin with correct credentials
3. Verify that products load and can be searched/filtered
4. Test error scenarios (wrong credentials, network issues)

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure your Odoo instance allows cross-origin requests from your Storyblok domain
2. **Authentication Failures**: Verify username, password, and database name
3. **No Products Showing**: Check that products are marked as `active=True` in Odoo
4. **Slow Loading**: Consider optimizing your Odoo instance or reducing the number of products

### Debug Mode

Enable browser developer tools to see detailed error messages and API requests.

## Future Enhancements

Potential improvements for future versions:

- Support for product variants
- Custom field mapping configuration
- Bulk product operations
- Integration with Odoo's multi-company features
- Support for different product models (e.g., `product.product`)
