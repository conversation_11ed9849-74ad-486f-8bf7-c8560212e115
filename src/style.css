html {
  overflow: auto;
  height: 100%;
}

#app {
  width: 100%;
  padding: 0 32px 32px;
}

/*
** Element Styles
*/
body {
  margin: 0;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
}

/*
** Main Design Token variables
*/
:root {
  --sb-color-base-50: #f6f7f8;
  --sb-color-base-700: #5f616e;

  --sb-color-primary-700: #05807f;

  --sb-color-secondary-100: #dde3ee;
  --sb-color-secondary-950: #1b243f;

  --sb-color-neutral-white: #ffffff;

  --sb-color-success-100: #d7f4e3;
  --sb-color-success-800: #13523b;

  --sb-color-danger-600: #e5271d;
}

/*
** Design System styles
*/
/* -- SbNotification */
.sb-notification.sb-notification--full {
  padding: 12px;
}
.sb-notification.sb-notification--negative {
  border: 1px solid var(--sb-color-danger-600);
}

/* -- SbButton */
.sb-button.sb-button--primary {
  background-color: var(--sb-color-primary-700);
}

/* -- SbCheckbox */
.sb-checkbox .sb-checkbox__native:checked + .sb-checkbox__input {
  background-color: var(--sb-color-primary-700);
}
.sb-checkbox .sb-checkbox__native:checked + .sb-checkbox__input {
  border-color: var(--sb-color-primary-700);
}

/* -- SbHeader */
.sb-header .sb-header__title {
  font-size: 20px;
}
.sb-header .sb-header__subtitle {
  margin: 0;
}
