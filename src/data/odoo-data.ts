import type { BasketItem, FilterOption } from '@/core'
import type { OdooConfig, OdooCategory } from '@/types/odoo'
import { OdooApiService } from '@/services/odoo-api'
import { 
  transformOdooProductToBasketItem, 
  transformOdooCategoriesToFilterOptions,
  getCategoryIdsFromStrings,
  isValidOdooProduct,
  isValidOdooCategory
} from '@/utils/odoo-transformer'

export type OdooItem = BasketItem<'item'> & {
  sku: string | undefined
  category: string | undefined
}

export class OdooDataService {
  private apiService: OdooApiService
  private categoriesCache: OdooCategory[] | null = null
  private categoriesCacheExpiry: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  constructor(config: OdooConfig) {
    this.apiService = new OdooApiService(config)
  }

  /**
   * Search for products with pagination, filtering, and search
   */
  async searchProducts(params: {
    searchTerm?: string
    categoryIds?: string[]
    page: number
    perPage: number
  }): Promise<{
    items: OdooItem[]
    totalCount: number
  }> {
    const { searchTerm, categoryIds = [], page, perPage } = params
    const offset = (page - 1) * perPage

    try {
      const numericCategoryIds = getCategoryIdsFromStrings(categoryIds)
      
      const result = await this.apiService.searchProducts({
        searchTerm,
        categoryIds: numericCategoryIds.length > 0 ? numericCategoryIds : undefined,
        limit: perPage,
        offset
      })

      // Transform and validate products
      const items = result.products
        .filter(isValidOdooProduct)
        .map(transformOdooProductToBasketItem)

      return {
        items,
        totalCount: result.totalCount
      }
    } catch (error) {
      console.error('Failed to search products:', error)
      throw new Error(
        `Failed to load products: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Get all categories for filtering (with caching)
   */
  async getCategories(): Promise<FilterOption[]> {
    try {
      // Check cache
      const now = Date.now()
      if (this.categoriesCache && now < this.categoriesCacheExpiry) {
        return transformOdooCategoriesToFilterOptions(this.categoriesCache)
      }

      // Fetch fresh data
      const categories = await this.apiService.searchCategories()
      
      // Validate and cache
      const validCategories = categories.filter(isValidOdooCategory)
      this.categoriesCache = validCategories
      this.categoriesCacheExpiry = now + this.CACHE_DURATION

      return transformOdooCategoriesToFilterOptions(validCategories)
    } catch (error) {
      console.error('Failed to fetch categories:', error)
      
      // Return cached data if available, otherwise empty array
      if (this.categoriesCache) {
        console.warn('Using cached categories due to API error')
        return transformOdooCategoriesToFilterOptions(this.categoriesCache)
      }
      
      throw new Error(
        `Failed to load categories: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Test the connection to Odoo
   */
  async testConnection(): Promise<boolean> {
    try {
      return await this.apiService.testConnection()
    } catch (error) {
      console.error('Connection test failed:', error)
      return false
    }
  }

  /**
   * Clear the categories cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.categoriesCache = null
    this.categoriesCacheExpiry = 0
  }
}

/**
 * Factory function to create OdooDataService instance
 */
export function createOdooDataService(config: OdooConfig): OdooDataService {
  return new OdooDataService(config)
}
