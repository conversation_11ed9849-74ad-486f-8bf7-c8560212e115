import type { BasketItem } from '@/core'
import type { OdooProduct, OdooCategory } from '@/types/odoo'
import type { FilterOption } from '@/core'

/**
 * Transforms an Odoo product into a BasketItem format
 */
export function transformOdooProductToBasketItem(product: OdooProduct): BasketItem<'item'> & { 
  sku: string | undefined
  category: string | undefined 
} {
  return {
    type: 'item',
    id: product.id.toString(),
    name: product.name,
    image: product.image_1920 ? `data:image/png;base64,${product.image_1920}` : undefined,
    description: product.description || undefined,
    sku: product.default_code || undefined,
    category: product.categ_id ? product.categ_id[1] : undefined,
  }
}

/**
 * Transforms Odoo categories into filter options
 */
export function transformOdooCategoriesToFilterOptions(categories: OdooCategory[]): FilterOption[] {
  return categories.map(category => ({
    value: category.id.toString(),
    label: category.name,
  }))
}

/**
 * Converts category names to category IDs for Odoo API queries
 */
export function getCategoryIdsFromNames(
  categoryNames: string[],
  categories: OdooCategory[]
): number[] {
  if (categoryNames.length === 0) {
    return []
  }

  return categories
    .filter(category => categoryNames.includes(category.name))
    .map(category => category.id)
}

/**
 * Converts category IDs (as strings) to numbers for Odoo API queries
 */
export function getCategoryIdsFromStrings(categoryIds: string[]): number[] {
  return categoryIds
    .map(id => parseInt(id, 10))
    .filter(id => !isNaN(id))
}

/**
 * Creates a display name for a product that includes SKU if available
 */
export function createProductDisplayName(product: OdooProduct): string {
  if (product.default_code) {
    return `${product.name} (${product.default_code})`
  }
  return product.name
}

/**
 * Validates if an Odoo product has the minimum required fields
 */
export function isValidOdooProduct(product: any): product is OdooProduct {
  return (
    typeof product === 'object' &&
    product !== null &&
    typeof product.id === 'number' &&
    typeof product.name === 'string' &&
    product.name.trim().length > 0
  )
}

/**
 * Validates if an Odoo category has the minimum required fields
 */
export function isValidOdooCategory(category: any): category is OdooCategory {
  return (
    typeof category === 'object' &&
    category !== null &&
    typeof category.id === 'number' &&
    typeof category.name === 'string' &&
    category.name.trim().length > 0
  )
}
