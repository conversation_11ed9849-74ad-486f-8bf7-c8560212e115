import { initials } from './initials'

describe('initials()', () => {
  it('Should give zero letters for an empty string', () => {
    expect(initials('')).toEqual('')
  })
  it('Should give one letter for a single word', () => {
    expect(initials('<PERSON>')).toEqual('J')
  })
  it('Should give two letters for a double words', () => {
    expect(initials('<PERSON>')).toEqual('JL')
  })
  it('Should give three letters for a triple words', () => {
    expect(initials('<PERSON>')).toEqual('JAL')
  })
  it('Should give two letters for a many words', () => {
    expect(initials('<PERSON>')).toEqual('JAT')
  })
  it('The letters should be capitalized', () => {
    expect(initials('johanne<PERSON> lind<PERSON>')).toEqual('JL')
  })
})
