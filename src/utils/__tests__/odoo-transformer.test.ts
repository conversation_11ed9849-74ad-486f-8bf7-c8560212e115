import { describe, it, expect } from 'vitest'
import {
  transformOdooProductToBasketItem,
  transformOdooCategoriesToFilterOptions,
  getCategoryIdsFromStrings,
  isValidOdooProduct,
  isValidOdooCategory
} from '../odoo-transformer'
import type { OdooProduct, OdooCategory } from '@/types/odoo'

describe('odoo-transformer', () => {
  describe('transformOdooProductToBasketItem', () => {
    it('should transform a complete Odoo product', () => {
      const odooProduct: OdooProduct = {
        id: 123,
        name: 'Test Product',
        default_code: 'TEST001',
        list_price: 99.99,
        image_1920: 'base64imagedata',
        description: 'A test product',
        categ_id: [1, 'Electronics'],
        active: true
      }

      const result = transformOdooProductToBasketItem(odooProduct)

      expect(result).toEqual({
        type: 'item',
        id: '123',
        name: 'Test Product',
        image: 'data:image/png;base64,base64imagedata',
        description: 'A test product',
        sku: 'TEST001',
        category: 'Electronics'
      })
    })

    it('should handle product with minimal data', () => {
      const odooProduct: OdooProduct = {
        id: 456,
        name: 'Minimal Product',
        list_price: 0,
        categ_id: [0, ''],
        active: true
      }

      const result = transformOdooProductToBasketItem(odooProduct)

      expect(result).toEqual({
        type: 'item',
        id: '456',
        name: 'Minimal Product',
        image: undefined,
        description: undefined,
        sku: undefined,
        category: ''
      })
    })

    it('should handle product without category', () => {
      const odooProduct: OdooProduct = {
        id: 789,
        name: 'No Category Product',
        list_price: 50,
        active: true
      } as any // Simulate missing categ_id

      const result = transformOdooProductToBasketItem(odooProduct)

      expect(result.category).toBeUndefined()
    })
  })

  describe('transformOdooCategoriesToFilterOptions', () => {
    it('should transform categories to filter options', () => {
      const categories: OdooCategory[] = [
        {
          id: 1,
          name: 'Electronics',
          parent_id: false,
          child_id: []
        },
        {
          id: 2,
          name: 'Clothing',
          parent_id: false,
          child_id: []
        }
      ]

      const result = transformOdooCategoriesToFilterOptions(categories)

      expect(result).toEqual([
        { value: '1', label: 'Electronics' },
        { value: '2', label: 'Clothing' }
      ])
    })

    it('should handle empty categories array', () => {
      const result = transformOdooCategoriesToFilterOptions([])
      expect(result).toEqual([])
    })
  })

  describe('getCategoryIdsFromStrings', () => {
    it('should convert string IDs to numbers', () => {
      const stringIds = ['1', '2', '3']
      const result = getCategoryIdsFromStrings(stringIds)
      expect(result).toEqual([1, 2, 3])
    })

    it('should filter out invalid IDs', () => {
      const stringIds = ['1', 'invalid', '3', 'NaN', '']
      const result = getCategoryIdsFromStrings(stringIds)
      expect(result).toEqual([1, 3])
    })

    it('should handle empty array', () => {
      const result = getCategoryIdsFromStrings([])
      expect(result).toEqual([])
    })
  })

  describe('isValidOdooProduct', () => {
    it('should validate a correct product', () => {
      const product = {
        id: 1,
        name: 'Valid Product',
        list_price: 100,
        active: true
      }

      expect(isValidOdooProduct(product)).toBe(true)
    })

    it('should reject product without ID', () => {
      const product = {
        name: 'Invalid Product',
        list_price: 100
      }

      expect(isValidOdooProduct(product)).toBe(false)
    })

    it('should reject product without name', () => {
      const product = {
        id: 1,
        list_price: 100
      }

      expect(isValidOdooProduct(product)).toBe(false)
    })

    it('should reject product with empty name', () => {
      const product = {
        id: 1,
        name: '   ',
        list_price: 100
      }

      expect(isValidOdooProduct(product)).toBe(false)
    })

    it('should reject null or undefined', () => {
      expect(isValidOdooProduct(null)).toBe(false)
      expect(isValidOdooProduct(undefined)).toBe(false)
    })
  })

  describe('isValidOdooCategory', () => {
    it('should validate a correct category', () => {
      const category = {
        id: 1,
        name: 'Valid Category',
        parent_id: false,
        child_id: []
      }

      expect(isValidOdooCategory(category)).toBe(true)
    })

    it('should reject category without ID', () => {
      const category = {
        name: 'Invalid Category'
      }

      expect(isValidOdooCategory(category)).toBe(false)
    })

    it('should reject category without name', () => {
      const category = {
        id: 1
      }

      expect(isValidOdooCategory(category)).toBe(false)
    })

    it('should reject category with empty name', () => {
      const category = {
        id: 1,
        name: ''
      }

      expect(isValidOdooCategory(category)).toBe(false)
    })
  })
})
