$card-padding-y: 15px;
$move-fade-transition:
  opacity 243ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
  transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

$breakpoint-mobile: 480;
$breakpoint-tablet: 768;
$breakpoint-desktop: 880;

$avatar-height: 32px;
$card-image-height: 140px;
$card-image-height-small: 80px;

$card-height: calc(#{$card-padding-y * 2 + $card-image-height-small});

@mixin transition($props...) {
  transition-property: $props;
  will-change: $props;
  transition-duration: 100ms;
  transition-timing-function: ease-in-out;
}

@mixin typography-title {
  color: var(--sb-color-secondary-950);
  font-family: Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: block;
  overflow: hidden;
  line-height: 15px;
  text-overflow: ellipsis;
}

@mixin typography-description {
  padding-left: 0;
  color: var(--sb-color-base-700);
  font-size: 12px;
  background-color: inherit;
  font-family: Roboto, sans-serif;
  text-overflow: ellipsis;
}

@mixin max-lines($lines) {
  overflow: hidden;
  display: -webkit-box;
  line-height: 1em;
  max-height: #{$lines}em;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}
