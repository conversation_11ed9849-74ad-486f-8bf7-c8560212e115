// Odoo JSON-RPC API types and interfaces

export interface OdooConfig {
  odooUrl: string
  database: string
  username: string
  password: string
}

export interface OdooJsonRpcRequest {
  jsonrpc: '2.0'
  id: number
  method: 'call'
  params: {
    service: 'object'
    method: 'execute_kw'
    args: [
      string, // database
      number, // user_id
      string, // password
      string, // model
      string, // operation (search, search_read, etc.)
      any[], // domain filters
      Record<string, any>? // additional options
    ]
  }
}

export interface OdooJsonRpcResponse<T = any> {
  jsonrpc: '2.0'
  id: number
  result?: T
  error?: {
    code: number
    message: string
    data?: any
  }
}

export interface OdooAuthResponse {
  uid: number
}

export interface OdooProduct {
  id: number
  name: string
  default_code?: string // SKU
  list_price: number
  image_1920?: string // Base64 encoded image
  description?: string
  categ_id: [number, string] // [category_id, category_name]
  active: boolean
}

export interface OdooCategory {
  id: number
  name: string
  parent_id: [number, string] | false
  child_id: number[]
}

export interface OdooSearchReadOptions {
  fields?: string[]
  limit?: number
  offset?: number
  order?: string
}

export interface OdooSearchResult<T> {
  records: T[]
  length: number
}

// Domain filter types for Odoo queries
export type OdooDomainFilter = 
  | [string, string, any] // ['field', 'operator', 'value']
  | '&' | '|' | '!' // logical operators

export interface ProductQueryParams {
  searchTerm?: string
  categoryIds?: number[]
  limit?: number
  offset?: number
}

export interface CategoryQueryParams {
  parentId?: number | false
}
