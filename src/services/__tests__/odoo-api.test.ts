import { describe, it, expect, vi, beforeEach } from 'vitest'
import { OdooApiService, OdooApiError } from '../odoo-api'
import type { OdooConfig } from '@/types/odoo'

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('OdooApiService', () => {
  const mockConfig: OdooConfig = {
    odooUrl: 'https://test-odoo.com',
    database: 'test_db',
    username: 'test_user',
    password: 'test_password'
  }

  let apiService: OdooApiService

  beforeEach(() => {
    vi.clearAllMocks()
    apiService = new OdooApiService(mockConfig)
  })

  describe('authentication', () => {
    it('should authenticate successfully', async () => {
      // Mock successful authentication response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 1,
          result: 123 // user ID
        })
      })

      const result = await apiService.testConnection()
      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-odoo.com/jsonrpc',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('"service":"common"')
        })
      )
    })

    it('should handle authentication failure', async () => {
      // Mock authentication failure
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 1,
          result: false
        })
      })

      const result = await apiService.testConnection()
      expect(result).toBe(false)
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await apiService.testConnection()
      expect(result).toBe(false)
    })
  })

  describe('searchProducts', () => {
    beforeEach(async () => {
      // Mock authentication
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 1,
          result: 123
        })
      })
    })

    it('should search products successfully', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Test Product',
          default_code: 'TEST001',
          list_price: 100.0,
          categ_id: [1, 'Test Category'],
          active: true
        }
      ]

      // Mock product search response
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            jsonrpc: '2.0',
            id: 2,
            result: mockProducts
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            jsonrpc: '2.0',
            id: 3,
            result: 1 // total count
          })
        })

      const result = await apiService.searchProducts({
        searchTerm: 'test',
        limit: 10,
        offset: 0
      })

      expect(result.products).toEqual(mockProducts)
      expect(result.totalCount).toBe(1)
    })

    it('should handle search with category filter', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            jsonrpc: '2.0',
            id: 2,
            result: []
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            jsonrpc: '2.0',
            id: 3,
            result: 0
          })
        })

      await apiService.searchProducts({
        categoryIds: [1, 2],
        limit: 10,
        offset: 0
      })

      // Verify the domain filter includes category filter
      const lastCall = mockFetch.mock.calls[mockFetch.mock.calls.length - 2]
      const requestBody = JSON.parse(lastCall[1].body)
      const domain = requestBody.params.args[5]
      
      expect(domain).toContainEqual(['categ_id', 'in', [1, 2]])
    })
  })

  describe('searchCategories', () => {
    beforeEach(async () => {
      // Mock authentication
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 1,
          result: 123
        })
      })
    })

    it('should fetch categories successfully', async () => {
      const mockCategories = [
        {
          id: 1,
          name: 'Electronics',
          parent_id: false,
          child_id: []
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 2,
          result: mockCategories
        })
      })

      const result = await apiService.searchCategories()
      expect(result).toEqual(mockCategories)
    })
  })

  describe('error handling', () => {
    it('should throw OdooApiError for API errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          jsonrpc: '2.0',
          id: 1,
          error: {
            code: 500,
            message: 'Internal Server Error',
            data: {}
          }
        })
      })

      await expect(apiService.testConnection()).rejects.toThrow(OdooApiError)
    })

    it('should throw OdooApiError for HTTP errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      await expect(apiService.testConnection()).rejects.toThrow(OdooApiError)
    })
  })
})
