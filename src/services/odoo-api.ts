import type {
  OdooConfig,
  OdooJsonRpcRequest,
  OdooJsonRpcResponse,
  OdooAuthResponse,
  OdooProduct,
  OdooCategory,
  OdooSearchReadOptions,
  OdooDomainFilter,
  ProductQueryParams,
  CategoryQueryParams,
} from '@/types/odoo'

export class OdooApiError extends Error {
  constructor(
    message: string,
    public code?: number,
    public data?: any
  ) {
    super(message)
    this.name = 'OdooApiError'
  }
}

export class OdooApiService {
  private config: OdooConfig
  private userId: number | null = null
  private requestId = 1

  constructor(config: OdooConfig) {
    this.config = config
  }

  private generateRequestId(): number {
    return this.requestId++
  }

  private async makeRequest<T = any>(
    model: string,
    method: string,
    args: any[] = [],
    options: Record<string, any> = {}
  ): Promise<T> {
    // Ensure we're authenticated
    if (!this.userId) {
      await this.authenticate()
    }

    const url = `${this.config.odooUrl.replace(/\/$/, '')}/jsonrpc`
    
    const requestBody: OdooJsonRpcRequest = {
      jsonrpc: '2.0',
      id: this.generateRequestId(),
      method: 'call',
      params: {
        service: 'object',
        method: 'execute_kw',
        args: [
          this.config.database,
          this.userId!,
          this.config.password,
          model,
          method,
          args,
          options
        ]
      }
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new OdooApiError(
          `HTTP error! status: ${response.status}`,
          response.status
        )
      }

      const data: OdooJsonRpcResponse<T> = await response.json()

      if (data.error) {
        throw new OdooApiError(
          data.error.message,
          data.error.code,
          data.error.data
        )
      }

      return data.result!
    } catch (error) {
      if (error instanceof OdooApiError) {
        throw error
      }
      throw new OdooApiError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  private async authenticate(): Promise<void> {
    const url = `${this.config.odooUrl.replace(/\/$/, '')}/jsonrpc`
    
    const requestBody = {
      jsonrpc: '2.0',
      id: this.generateRequestId(),
      method: 'call',
      params: {
        service: 'common',
        method: 'authenticate',
        args: [
          this.config.database,
          this.config.username,
          this.config.password,
          {}
        ]
      }
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new OdooApiError(
          `Authentication failed: HTTP ${response.status}`,
          response.status
        )
      }

      const data: OdooJsonRpcResponse<number> = await response.json()

      if (data.error) {
        throw new OdooApiError(
          `Authentication failed: ${data.error.message}`,
          data.error.code,
          data.error.data
        )
      }

      if (!data.result) {
        throw new OdooApiError('Authentication failed: Invalid credentials')
      }

      this.userId = data.result
    } catch (error) {
      if (error instanceof OdooApiError) {
        throw error
      }
      throw new OdooApiError(
        `Authentication error: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  async searchProducts(params: ProductQueryParams = {}): Promise<{
    products: OdooProduct[]
    totalCount: number
  }> {
    const { searchTerm, categoryIds, limit = 20, offset = 0 } = params

    // Build domain filters
    const domain: OdooDomainFilter[] = [
      ['active', '=', true], // Only active products
    ]

    // Add search term filter
    if (searchTerm && searchTerm.trim()) {
      domain.push('|')
      domain.push(['name', 'ilike', searchTerm.trim()])
      domain.push(['default_code', 'ilike', searchTerm.trim()])
    }

    // Add category filter
    if (categoryIds && categoryIds.length > 0) {
      if (categoryIds.length === 1) {
        domain.push(['categ_id', '=', categoryIds[0]])
      } else {
        domain.push(['categ_id', 'in', categoryIds])
      }
    }

    const options: OdooSearchReadOptions = {
      fields: ['id', 'name', 'default_code', 'list_price', 'image_1920', 'description', 'categ_id'],
      limit,
      offset,
      order: 'name asc'
    }

    try {
      // Get products
      const products = await this.makeRequest<OdooProduct[]>(
        'product.template',
        'search_read',
        [domain],
        options
      )

      // Get total count
      const totalCount = await this.makeRequest<number>(
        'product.template',
        'search_count',
        [domain]
      )

      return {
        products,
        totalCount
      }
    } catch (error) {
      throw new OdooApiError(
        `Failed to fetch products: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  async searchCategories(params: CategoryQueryParams = {}): Promise<OdooCategory[]> {
    const { parentId } = params

    const domain: OdooDomainFilter[] = []

    // Filter by parent category if specified
    if (parentId !== undefined) {
      domain.push(['parent_id', '=', parentId])
    }

    const options: OdooSearchReadOptions = {
      fields: ['id', 'name', 'parent_id', 'child_id'],
      order: 'name asc'
    }

    try {
      return await this.makeRequest<OdooCategory[]>(
        'product.category',
        'search_read',
        [domain],
        options
      )
    } catch (error) {
      throw new OdooApiError(
        `Failed to fetch categories: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.authenticate()
      return true
    } catch (error) {
      return false
    }
  }
}
