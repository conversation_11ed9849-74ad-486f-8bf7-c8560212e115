import { StoryblokIcon } from '@/components'
import { useErrorNotification } from '@/composables'
import { createOdooDataService } from '@/data/odoo-data'
import type { OdooConfig } from '@/types/odoo'
import {
  defineConfig,
  type TabItemError,
  type PickerConfig,
} from '@/core'

export default defineConfig((options) => {
  const config: PickerConfig = {
    title: 'Odoo Product Picker',
    icon: StoryblokIcon,
  }

  const { setErrorNotification } = useErrorNotification()

  try {
    config.validateOptions = () => {
      const { odooUrl, database, username, password, limit } = options

      // Validate required Odoo connection options
      if (!odooUrl || typeof odooUrl !== 'string' || !odooUrl.trim()) {
        return {
          isValid: false,
          error: '`odooUrl` is required and must be a valid URL',
        }
      }

      if (!database || typeof database !== 'string' || !database.trim()) {
        return {
          isValid: false,
          error: '`database` name is required',
        }
      }

      if (!username || typeof username !== 'string' || !username.trim()) {
        return {
          isValid: false,
          error: '`username` is required',
        }
      }

      if (!password || typeof password !== 'string' || !password.trim()) {
        return {
          isValid: false,
          error: '`password` is required',
        }
      }

      // Validate optional limit
      const isLimitOptionValid = limit === undefined || limit === '' || Number(limit) > 0

      if (!isLimitOptionValid) {
        return {
          isValid: false,
          error: `The 'limit' option must be an integer greater than 0`,
        }
      }

      return {
        isValid: true,
      }
    }

    // Create Odoo data service with validated options
    const odooConfig: OdooConfig = {
      odooUrl: options.odooUrl as string,
      database: options.database as string,
      username: options.username as string,
      password: options.password as string,
    }

    const odooDataService = createOdooDataService(odooConfig)

    config.tabs = [
      {
        name: 'products',
        label: 'Products',
        query: async ({ searchTerm, page, perPage, filterSelection }) => {
          try {
            const categoryIds = filterSelection['categoryMulti'] as string[] || []

            const result = await odooDataService.searchProducts({
              searchTerm: searchTerm || undefined,
              categoryIds,
              page,
              perPage,
            })

            return {
              items: result.items,
              pageInfo: {
                totalCount: result.totalCount,
              },
            }
          } catch (error) {
            console.error('Failed to query products:', error)
            throw new Error(
              `Failed to load products: ${error instanceof Error ? error.message : 'Unknown error'}`
            )
          }
        },
        getFilters: async () => {
          try {
            const categoryOptions = await odooDataService.getCategories()

            return [
              {
                type: 'multi',
                label: 'Categories',
                name: 'categoryMulti',
                defaultValue: [],
                options: categoryOptions,
              },
            ]
          } catch (error) {
            console.error('Failed to load categories:', error)
            throw new Error(
              `Failed to load categories: ${error instanceof Error ? error.message : 'Unknown error'}`
            )
          }
        },
        onError: (tabItemError: TabItemError) => {
          const notificationTitle =
            tabItemError.type === 'queryError'
              ? 'Failed to fetch products from Odoo'
              : 'Failed to load categories from Odoo'

          const errorMessage = tabItemError.error?.message || 'Unknown error occurred'

          // Check for common Odoo connection issues
          let userFriendlyMessage = errorMessage
          if (errorMessage.includes('Authentication failed')) {
            userFriendlyMessage = 'Please check your Odoo credentials (username/password)'
          } else if (errorMessage.includes('Network error') || errorMessage.includes('HTTP error')) {
            userFriendlyMessage = 'Please check your Odoo URL and network connection'
          } else if (errorMessage.includes('database')) {
            userFriendlyMessage = 'Please check your database name'
          }

          setErrorNotification({
            location: 'modal',
            title: notificationTitle,
            message: userFriendlyMessage,
          })

          return false
        },
      },
    ]
  } catch (err) {
    setErrorNotification({
      location: 'main',
      title: 'Plugin could not be initialized: ' + (err as Error).message,
    })
  }

  return config
})
