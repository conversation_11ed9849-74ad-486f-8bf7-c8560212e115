{"name": "picker-starter", "private": false, "version": "0.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vitest", "prettier": "prettier --check .", "lint": "eslint .", "check:types": "vue-tsc --noEmit", "preview": "vite preview", "deploy": "npm run build && npx @storyblok/field-plugin-cli@latest deploy"}, "dependencies": {"@storyblok/design-system": "3.19.3", "@storyblok/field-plugin": "^1.4.0", "debounce": "^1.2.1", "vue": "^3.2.47", "vue-draggable-next": "^2.2.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@types/debounce": "^1.2.4", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^4.1.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "sass": "^1.69.5", "ts-node": "^10.9.1", "typescript": "5.1.6", "vite": "^4.2.2", "vite-plugin-css-injected-by-js": "^3.3.0", "vitest": "^0.34.6", "vue-tsc": "1.8.6"}}